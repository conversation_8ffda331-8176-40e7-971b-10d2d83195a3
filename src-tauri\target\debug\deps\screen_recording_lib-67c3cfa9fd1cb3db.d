D:\github\Screen Recording\src-tauri\target\debug\deps\screen_recording_lib-67c3cfa9fd1cb3db.d: src\lib.rs D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\github\Screen Recording\src-tauri\target\debug\deps\libscreen_recording_lib-67c3cfa9fd1cb3db.rmeta: src\lib.rs D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\lib.rs:
D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=screen-recording
# env-dep:OUT_DIR=D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\screen-recording-995c3a151ab02581\\out
