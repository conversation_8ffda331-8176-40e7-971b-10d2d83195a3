{"$schema": "http://json-schema.org/draft-07/schema#", "title": "ShellScopeEntry", "description": "Shell scope entry.", "anyOf": [{"type": "object", "required": ["cmd", "name"], "properties": {"name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "cmd": {"description": "The command name. It can start with a variable that resolves to a system base directory. The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`, `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`, `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`, `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "type": "string"}, "args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}}, "additionalProperties": false}, {"type": "object", "required": ["name", "sidecar"], "properties": {"name": {"description": "The name for this allowed shell command configuration.\n\nThis name will be used inside of the webview API to call this command along with any specified arguments.", "type": "string"}, "args": {"description": "The allowed arguments for the command execution.", "allOf": [{"$ref": "#/definitions/ShellScopeEntryAllowedArgs"}]}, "sidecar": {"description": "If this command is a sidecar command.", "type": "boolean"}}, "additionalProperties": false}], "definitions": {"ShellScopeEntryAllowedArgs": {"description": "A set of command arguments allowed to be executed by the webview API.\n\nA value of `true` will allow any arguments to be passed to the command. `false` will disable all arguments. A list of [`ShellScopeEntryAllowedArg`] will set those arguments as the only valid arguments to be passed to the attached command configuration.", "anyOf": [{"description": "Use a simple boolean to allow all or disable all arguments to this command configuration.", "type": "boolean"}, {"description": "A specific set of [`ShellScopeEntryAllowedArg`] that are valid to call for the command configuration.", "type": "array", "items": {"$ref": "#/definitions/ShellScopeEntryAllowedArg"}}]}, "ShellScopeEntryAllowedArg": {"description": "A command argument allowed to be executed by the webview API.", "anyOf": [{"description": "A non-configurable argument that is passed to the command in the order it was specified.", "type": "string"}, {"description": "A variable that is set while calling the command from the webview API.", "type": "object", "required": ["validator"], "properties": {"validator": {"description": "[regex] validator to require passed values to conform to an expected input.\n\nThis will require the argument value passed to this variable to match the `validator` regex before it will be executed.\n\nThe regex string is by default surrounded by `^...$` to match the full string. For example the `https?://\\w+` regex would be registered as `^https?://\\w+$`.\n\n[regex]: <https://docs.rs/regex/latest/regex/#syntax>", "type": "string"}, "raw": {"description": "Marks the validator as a raw regex, meaning the plugin should not make any modification at runtime.\n\nThis means the regex will not match on the entire string by default, which might be exploited if your regex allow unexpected input to be considered valid. When using this option, make sure your regex is correct.", "default": false, "type": "boolean"}}, "additionalProperties": false}]}}}