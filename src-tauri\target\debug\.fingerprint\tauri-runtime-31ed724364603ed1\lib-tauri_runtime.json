{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 6604126508699635284, "deps": [[2013030631243296465, "webview2_com", false, 5927907557082522975], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [4352886507220678900, "serde_json", false, 8129347805295222210], [4537297827336760846, "thiserror", false, 9390898151654131111], [5404511084185685755, "url", false, 10335955844979737016], [7606335748176206944, "dpi", false, 12098818709625280286], [9010263965687315507, "http", false, 13386383315247088926], [9689903380558560274, "serde", false, 3343704796167280671], [14585479307175734061, "windows", false, 1114852230663379918], [16727543399706004146, "cookie", false, 13570237757606925530], [17233053221795943287, "tauri_utils", false, 15618318111205922455], [18010483002580779355, "build_script_build", false, 5405614531958938770]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-31ed724364603ed1\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}