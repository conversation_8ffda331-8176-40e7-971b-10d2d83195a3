<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import type { RecordingRegion } from '$lib/stores/recording';

  const dispatch = createEventDispatcher<{
    confirm: RecordingRegion;
    cancel: void;
  }>();

  let overlayElement: HTMLDivElement;
  let selectorElement: HTMLDivElement;
  let isSelecting = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;

  // 当前选择的区域 - 使用屏幕坐标
  let region: RecordingRegion = { x: 0, y: 0, width: 0, height: 0 };
  
  // 屏幕信息（通过全局变量或者从Tauri获取）
  let screenOffsetX = 0;
  let screenOffsetY = 0;

  /**
   * 获取当前窗口的屏幕偏移量
   */
  async function getScreenOffset() {
    if (typeof window !== 'undefined' && window.__TAURI_INTERNALS__) {
      try {
        // 通过Tauri获取当前窗口位置
        const position = await window.__TAURI_INTERNALS__.invoke('get_current_window_position');
        screenOffsetX = position.x || 0;
        screenOffsetY = position.y || 0;
      } catch (error) {
        console.warn('无法获取窗口位置，使用默认值:', error);
        screenOffsetX = 0;
        screenOffsetY = 0;
      }
    }
  }

  /**
   * 处理鼠标按下开始选择
   */
  function handleMouseDown(event: MouseEvent) {
    // 防止在选择框内部点击时重新开始选择
    if (event.target !== overlayElement) return;
    
    isSelecting = true;
    // 使用页面坐标，这些已经是相对于屏幕的
    startX = event.pageX;
    startY = event.pageY;
    currentX = event.pageX;
    currentY = event.pageY;
    
    updateSelector();
  }

  /**
   * 处理鼠标移动更新选择区域
   */
  function handleMouseMove(event: MouseEvent) {
    if (!isSelecting) return;
    
    currentX = event.pageX;
    currentY = event.pageY;
    
    updateSelector();
  }

  /**
   * 处理鼠标抬起完成选择
   */
  function handleMouseUp() {
    if (!isSelecting) return;
    
    isSelecting = false;
    
    // 计算最终区域（屏幕坐标）
    const left = Math.min(startX, currentX);
    const top = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);
    
    if (width > 10 && height > 10) {
      // 转换为屏幕坐标
      region = { 
        x: left + screenOffsetX, 
        y: top + screenOffsetY, 
        width, 
        height 
      };
      updateSelector();
    }
  }

  /**
   * 更新选择器位置和大小
   */
  function updateSelector() {
    if (!selectorElement) return;
    
    const left = Math.min(startX, currentX);
    const top = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);
    
    selectorElement.style.left = `${left}px`;
    selectorElement.style.top = `${top}px`;
    selectorElement.style.width = `${width}px`;
    selectorElement.style.height = `${height}px`;
    selectorElement.style.display = width > 0 && height > 0 ? 'block' : 'none';
  }

  /**
   * 确认选择区域
   */
  function confirmSelection() {
    if (region.width > 10 && region.height > 10) {
      dispatch('confirm', region);
    }
  }

  /**
   * 取消选择
   */
  function cancelSelection() {
    dispatch('cancel');
  }

  /**
   * 处理键盘事件
   */
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      cancelSelection();
    } else if (event.key === 'Enter' && region.width > 10 && region.height > 10) {
      confirmSelection();
    }
  }

  onMount(async () => {
    // 获取屏幕偏移量
    await getScreenOffset();
    
    // 防止页面滚动和选择文本
    document.body.style.overflow = 'hidden';
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    
    // 设置鼠标样式
    document.body.style.cursor = 'crosshair';
    
    return () => {
      document.body.style.overflow = '';
      document.body.style.userSelect = '';
      document.body.style.webkitUserSelect = '';
      document.body.style.cursor = '';
    };
  });
</script>

<svelte:window on:keydown={handleKeyDown} />

<div 
  class="fullscreen-region-overlay"
  bind:this={overlayElement}
  on:mousedown={handleMouseDown}
  on:mousemove={handleMouseMove}
  on:mouseup={handleMouseUp}
  role="button"
  tabindex="0"
>
  <!-- 背景遮罩层 -->
  <div class="background-mask"></div>
  
  <!-- 选择框 -->
  <div 
    class="selection-box"
    bind:this={selectorElement}
  >
    <!-- 选择信息显示 -->
    {#if region.width > 10 && region.height > 10}
      <div class="selection-info">
        {region.width} × {region.height}
        <br>
        <small>位置: ({region.x}, {region.y})</small>
      </div>
    {/if}
  </div>

  <!-- 中央提示信息 -->
  <div class="instruction-overlay">
    <div class="instruction-content">
      <h3>🎯 选择录制区域</h3>
      <p>在屏幕上拖拽鼠标选择要录制的区域</p>
      <p class="instruction-hint">可以跨越整个屏幕范围进行选择</p>
      <div class="instruction-shortcuts">
        <span><kbd>Enter</kbd> 确认选择</span>
        <span><kbd>Esc</kbd> 取消选择</span>
      </div>
    </div>
  </div>

  <!-- 控制按钮 -->
  {#if region.width > 10 && region.height > 10}
    <div class="control-buttons">
      <button class="btn btn-primary" on:click={confirmSelection}>
        ✓ 确认选择
      </button>
      <button class="btn btn-secondary" on:click={cancelSelection}>
        ✕ 取消
      </button>
    </div>
  {/if}
</div>

<style>
  .fullscreen-region-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
    user-select: none;
    -webkit-user-select: none;
    cursor: crosshair;
  }

  .background-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    pointer-events: none;
  }

  .selection-box {
    position: absolute;
    border: 3px solid #4CAF50;
    background: rgba(76, 175, 80, 0.1);
    display: none;
    pointer-events: none;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8),
                inset 0 0 0 1px rgba(255, 255, 255, 0.8);
  }

  .selection-info {
    position: absolute;
    top: -60px;
    left: 0;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    white-space: nowrap;
    font-family: monospace;
    border: 1px solid #4CAF50;
  }

  .selection-info small {
    opacity: 0.8;
    font-size: 0.75rem;
  }

  .instruction-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.98);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: center;
    pointer-events: none;
    max-width: 500px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .instruction-content h3 {
    margin: 0 0 1rem 0;
    color: #2E7D32;
    font-size: 1.75rem;
    font-weight: 700;
  }

  .instruction-content p {
    margin: 0 0 1rem 0;
    color: #424242;
    font-size: 1.1rem;
    line-height: 1.5;
  }

  .instruction-hint {
    font-size: 0.95rem !important;
    color: #666 !important;
    font-style: italic;
  }

  .instruction-shortcuts {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 1.5rem;
  }

  .instruction-shortcuts span {
    color: #666;
    font-size: 0.9rem;
    font-weight: 500;
  }

  kbd {
    display: inline-block;
    padding: 0.3rem 0.6rem;
    font-size: 0.8rem;
    color: #333;
    background: linear-gradient(145deg, #f5f5f5, #e8e8e8);
    border: 1px solid #ccc;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    margin-right: 0.4rem;
    font-weight: 600;
  }

  .control-buttons {
    position: absolute;
    bottom: 3rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 10001;
  }

  .btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .btn-primary {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  }

  .btn-secondary {
    background: linear-gradient(145deg, #757575, #616161);
    color: white;
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(117, 117, 117, 0.4);
  }

  .btn:active {
    transform: translateY(0px);
  }

  /* 深色主题适配 */
  @media (prefers-color-scheme: dark) {
    .background-mask {
      background: rgba(0, 0, 0, 0.6);
    }

    .selection-box {
      border-color: #66BB6A;
      background: rgba(102, 187, 106, 0.15);
    }

    .selection-info {
      background: rgba(33, 33, 33, 0.95);
      border-color: #66BB6A;
    }

    .instruction-overlay {
      background: rgba(33, 33, 33, 0.98);
      color: #f5f5f5;
    }

    .instruction-content h3 {
      color: #66BB6A;
    }

    .instruction-content p {
      color: #e0e0e0;
    }

    .instruction-hint {
      color: #bbb !important;
    }

    .instruction-shortcuts span {
      color: #bbb;
    }

    kbd {
      background: linear-gradient(145deg, #424242, #333);
      border-color: #555;
      color: #f5f5f5;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
  }

  /* 动画效果 */
  .selection-box {
    animation: selection-appear 0.2s ease-out;
  }

  @keyframes selection-appear {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .instruction-overlay {
    animation: fade-in 0.5s ease-out;
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  .control-buttons {
    animation: slide-up 0.3s ease-out;
  }

  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }
</style>