{"rustc": 3062648155896360161, "features": "[\"macos-private-api\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 6604126508699635284, "deps": [[2013030631243296465, "webview2_com", false, 13060970121218249020], [4143744114649553716, "raw_window_handle", false, 6746238607539022165], [4352886507220678900, "serde_json", false, 3514748254459580741], [4537297827336760846, "thiserror", false, 14182969183628281155], [5404511084185685755, "url", false, 8679425891305281097], [7606335748176206944, "dpi", false, 6709290295000054096], [9010263965687315507, "http", false, 5658707743831154247], [9689903380558560274, "serde", false, 15997447206965605229], [14585479307175734061, "windows", false, 7951036826101499968], [16727543399706004146, "cookie", false, 12847614340541995132], [17233053221795943287, "tauri_utils", false, 7279738404176155259], [18010483002580779355, "build_script_build", false, 17051467136768127715]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-be2107e443d7ae2f\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}