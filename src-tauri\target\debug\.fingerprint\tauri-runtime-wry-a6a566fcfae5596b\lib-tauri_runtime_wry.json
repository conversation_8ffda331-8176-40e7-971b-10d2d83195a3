{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 2684675381532056621, "deps": [[376837177317575824, "softbuffer", false, 2989777400569771225], [2013030631243296465, "webview2_com", false, 13060970121218249020], [2172092324659420098, "tao", false, 16021378859300817556], [3722963349756955755, "once_cell", false, 15594208106530447370], [4143744114649553716, "raw_window_handle", false, 6746238607539022165], [5404511084185685755, "url", false, 8679425891305281097], [5986029879202738730, "log", false, 7362680585957259537], [9010263965687315507, "http", false, 5658707743831154247], [9952368442187680820, "build_script_build", false, 12913848232493947529], [10279713220953215556, "wry", false, 10886364264442394909], [14585479307175734061, "windows", false, 7951036826101499968], [17233053221795943287, "tauri_utils", false, 9801541900573708252], [18010483002580779355, "tauri_runtime", false, 2008724745218435212]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-a6a566fcfae5596b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}