{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 13021828739765011656, "deps": [[503635761244294217, "regex", false, 4881237336441774592], [1200537532907108615, "url<PERSON><PERSON>n", false, 13970908537300600685], [1678291836268844980, "brotli", false, 5238807098627649844], [2995469292676432503, "uuid", false, 10895018417115143966], [4071963112282141418, "serde_with", false, 9281286822945221231], [4352886507220678900, "serde_json", false, 8129347805295222210], [4537297827336760846, "thiserror", false, 9390898151654131111], [4899080583175475170, "semver", false, 1854357184452311336], [5404511084185685755, "url", false, 10335955844979737016], [5986029879202738730, "log", false, 7774817665610071283], [6606131838865521726, "ctor", false, 5478961089336400257], [7170110829644101142, "json_patch", false, 11444664863209536082], [9010263965687315507, "http", false, 13386383315247088926], [9293239362693504808, "glob", false, 4660908313084390176], [9689903380558560274, "serde", false, 3343704796167280671], [11207653606310558077, "anyhow", false, 536122387551949549], [11989259058781683633, "dunce", false, 6908640956392211055], [12060164242600251039, "toml", false, 7469775710858970519], [15622660310229662834, "walkdir", false, 17721089389136930611], [15932120279885307830, "memchr", false, 16908306198200602351], [17146114186171651583, "infer", false, 13634844287413421365], [17183029615630212089, "serde_untagged", false, 5091106841543681013], [17186037756130803222, "phf", false, 11719316345516376416]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-5e85d6fba35e867c\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}