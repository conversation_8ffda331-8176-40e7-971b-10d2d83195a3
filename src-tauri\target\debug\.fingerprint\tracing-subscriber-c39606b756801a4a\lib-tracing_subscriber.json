{"rustc": 3062648155896360161, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 17519360140317990030, "deps": [[503635761244294217, "regex", false, 4881237336441774592], [1009387600818341822, "matchers", false, 9329179303111490241], [1017461770342116999, "sharded_slab", false, 3516351061815804773], [1359731229228270592, "thread_local", false, 2607508518893861172], [3424551429995674438, "tracing_core", false, 5038284067902823902], [3666196340704888985, "smallvec", false, 15656184802079920768], [3722963349756955755, "once_cell", false, 12245490560791113519], [8606274917505247608, "tracing", false, 1705463880016604026], [8614575489689151157, "nu_ansi_term", false, 12027747428113542517], [10806489435541507125, "tracing_log", false, 17300438238061590379]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-c39606b756801a4a\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}