{"rustc": 3062648155896360161, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 3254806551126565539, "path": 5968889965917471596, "deps": [[2013030631243296465, "webview2_com", false, 5927907557082522975], [3334271191048661305, "windows_version", false, 10423645401369898531], [3722963349756955755, "once_cell", false, 12245490560791113519], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [4537297827336760846, "thiserror", false, 9390898151654131111], [5628259161083531273, "windows_core", false, 2737255917552066159], [7606335748176206944, "dpi", false, 12098818709625280286], [9010263965687315507, "http", false, 13386383315247088926], [10279713220953215556, "build_script_build", false, 4520074807294947611], [11989259058781683633, "dunce", false, 6908640956392211055], [14585479307175734061, "windows", false, 1114852230663379918], [16727543399706004146, "cookie", false, 13570237757606925530]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-2842bf5b1fa95aa8\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}