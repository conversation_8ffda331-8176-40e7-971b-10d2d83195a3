<script lang="ts">
  import { onMount } from 'svelte';
  import FullscreenRegionSelector from '$lib/components/FullscreenRegionSelector.svelte';
  
  let showSelector = true;
  
  /**
   * 处理区域选择确认
   */
  function handleRegionConfirm(event: CustomEvent) {
    // 向主窗口发送选择的区域信息
    if (typeof window !== 'undefined') {
      window.__TAURI_INTERNALS__.invoke('region_selected', {
        region: event.detail
      });
    }
    showSelector = false;
  }
  
  /**
   * 处理区域选择取消
   */
  function handleRegionCancel() {
    // 向主窗口发送取消信息
    if (typeof window !== 'undefined') {
      window.__TAURI_INTERNALS__.invoke('region_selection_cancelled', {});
    }
    showSelector = false;
  }
  
  onMount(() => {
    // 防止页面右键菜单等
    document.addEventListener('contextmenu', e => e.preventDefault());
    document.addEventListener('dragstart', e => e.preventDefault());
    document.addEventListener('selectstart', e => e.preventDefault());
    
    return () => {
      document.removeEventListener('contextmenu', e => e.preventDefault());
      document.removeEventListener('dragstart', e => e.preventDefault());
      document.removeEventListener('selectstart', e => e.preventDefault());
    };
  });
</script>

<svelte:head>
  <title>区域选择器</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      background: transparent;
      user-select: none;
    }
  </style>
</svelte:head>

{#if showSelector}
  <FullscreenRegionSelector 
    on:confirm={handleRegionConfirm}
    on:cancel={handleRegionCancel}
  />
{/if}

<style>
  :global(html, body) {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: transparent;
    user-select: none;
    -webkit-user-select: none;
  }
</style>