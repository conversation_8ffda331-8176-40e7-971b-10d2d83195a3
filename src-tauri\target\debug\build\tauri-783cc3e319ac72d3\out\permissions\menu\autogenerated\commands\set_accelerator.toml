# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-accelerator"
description = "Enables the set_accelerator command without any pre-configured scope."
commands.allow = ["set_accelerator"]

[[permission]]
identifier = "deny-set-accelerator"
description = "Denies the set_accelerator command without any pre-configured scope."
commands.deny = ["set_accelerator"]
