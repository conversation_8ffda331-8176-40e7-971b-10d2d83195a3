<script lang="ts">
  import { onMount, createEventDispatcher } from 'svelte';
  import type { RecordingRegion } from '$lib/stores/recording';

  const dispatch = createEventDispatcher<{
    confirm: RecordingRegion;
    cancel: void;
  }>();

  let overlayElement: HTMLDivElement;
  let selectorElement: HTMLDivElement;
  let isSelecting = false;
  let startX = 0;
  let startY = 0;
  let currentX = 0;
  let currentY = 0;

  // 当前选择的区域
  let region: RecordingRegion = { x: 0, y: 0, width: 0, height: 0 };

  /**
   * 处理鼠标按下开始选择
   */
  function handleMouseDown(event: MouseEvent) {
    if (event.target !== overlayElement) return;
    
    isSelecting = true;
    startX = event.clientX;
    startY = event.clientY;
    currentX = event.clientX;
    currentY = event.clientY;
    
    updateSelector();
  }

  /**
   * 处理鼠标移动更新选择区域
   */
  function handleMouseMove(event: MouseEvent) {
    if (!isSelecting) return;
    
    currentX = event.clientX;
    currentY = event.clientY;
    
    updateSelector();
  }

  /**
   * 处理鼠标抬起完成选择
   */
  function handleMouseUp() {
    if (!isSelecting) return;
    
    isSelecting = false;
    
    // 计算最终区域
    const left = Math.min(startX, currentX);
    const top = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);
    
    if (width > 10 && height > 10) {
      region = { x: left, y: top, width, height };
      updateSelector();
    }
  }

  /**
   * 更新选择器位置和大小
   */
  function updateSelector() {
    if (!selectorElement) return;
    
    const left = Math.min(startX, currentX);
    const top = Math.min(startY, currentY);
    const width = Math.abs(currentX - startX);
    const height = Math.abs(currentY - startY);
    
    selectorElement.style.left = `${left}px`;
    selectorElement.style.top = `${top}px`;
    selectorElement.style.width = `${width}px`;
    selectorElement.style.height = `${height}px`;
    selectorElement.style.display = width > 0 && height > 0 ? 'block' : 'none';
  }

  /**
   * 确认选择区域
   */
  function confirmSelection() {
    if (region.width > 10 && region.height > 10) {
      dispatch('confirm', region);
    }
  }

  /**
   * 取消选择
   */
  function cancelSelection() {
    dispatch('cancel');
  }

  /**
   * 处理键盘事件
   */
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      cancelSelection();
    } else if (event.key === 'Enter' && region.width > 10 && region.height > 10) {
      confirmSelection();
    }
  }

  onMount(() => {
    // 防止页面滚动
    document.body.style.overflow = 'hidden';
    
    return () => {
      document.body.style.overflow = '';
    };
  });
</script>

<svelte:window on:keydown={handleKeyDown} />

<div 
  class="region-selector-overlay"
  bind:this={overlayElement}
  on:mousedown={handleMouseDown}
  on:mousemove={handleMouseMove}
  on:mouseup={handleMouseUp}
  role="button"
  tabindex="0"
>
  <!-- 选择框 -->
  <div 
    class="selection-box"
    bind:this={selectorElement}
  >
    <!-- 选择信息显示 -->
    {#if region.width > 10 && region.height > 10}
      <div class="selection-info">
        {region.width} × {region.height}
      </div>
    {/if}
  </div>

  <!-- 提示信息 -->
  <div class="instruction-overlay">
    <div class="instruction-content">
      <h3>选择录制区域</h3>
      <p>拖拽鼠标选择要录制的屏幕区域</p>
      <div class="instruction-shortcuts">
        <span><kbd>Enter</kbd> 确认</span>
        <span><kbd>Esc</kbd> 取消</span>
      </div>
    </div>
  </div>

  <!-- 控制按钮 -->
  {#if region.width > 10 && region.height > 10}
    <div class="control-buttons">
      <button class="btn btn-primary" on:click={confirmSelection}>
        确认选择
      </button>
      <button class="btn btn-secondary" on:click={cancelSelection}>
        取消
      </button>
    </div>
  {/if}
</div>

<style>
  .region-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    cursor: crosshair;
    z-index: 9999;
    user-select: none;
  }

  .selection-box {
    position: absolute;
    border: 2px solid #667eea;
    background: rgba(102, 126, 234, 0.1);
    display: none;
    pointer-events: none;
  }

  .selection-info {
    position: absolute;
    top: -40px;
    left: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
  }

  .instruction-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    pointer-events: none;
  }

  .instruction-content h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.5rem;
  }

  .instruction-content p {
    margin: 0 0 1.5rem 0;
    color: #666;
    font-size: 1rem;
  }

  .instruction-shortcuts {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .instruction-shortcuts span {
    color: #666;
    font-size: 0.875rem;
  }

  kbd {
    display: inline-block;
    padding: 0.2rem 0.4rem;
    font-size: 0.75rem;
    color: #333;
    background: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 0 #ccc;
    font-family: monospace;
    margin-right: 0.25rem;
  }

  .control-buttons {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
  }

  .btn-secondary {
    background: #f5f5f5;
    color: #333;
  }

  .btn-secondary:hover {
    background: #e0e0e0;
  }

  @media (prefers-color-scheme: dark) {
    .instruction-overlay {
      background: rgba(30, 30, 30, 0.95);
    }

    .instruction-content h3 {
      color: #f5f5f5;
    }

    .instruction-content p {
      color: #aaa;
    }

    .instruction-shortcuts span {
      color: #aaa;
    }

    kbd {
      background: #2c2c2c;
      border-color: #444;
      color: #f5f5f5;
      box-shadow: 0 2px 0 #444;
    }

    .btn-secondary {
      background: #2c2c2c;
      color: #f5f5f5;
    }

    .btn-secondary:hover {
      background: #3c3c3c;
    }
  }
</style>