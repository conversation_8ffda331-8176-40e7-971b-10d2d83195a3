# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-set-ignore-cursor-events"
description = "Enables the set_ignore_cursor_events command without any pre-configured scope."
commands.allow = ["set_ignore_cursor_events"]

[[permission]]
identifier = "deny-set-ignore-cursor-events"
description = "Denies the set_ignore_cursor_events command without any pre-configured scope."
commands.deny = ["set_ignore_cursor_events"]
