{"rustc": 3062648155896360161, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 16594089302644043547, "deps": [[373107762698212489, "proc_macro2", false, 17845992585597592650], [3412097196613774653, "tauri_codegen", false, 7113133753919957661], [13077543566650298139, "heck", false, 3582930420090025893], [17233053221795943287, "tauri_utils", false, 6114817519717018938], [17332570067994900305, "syn", false, 14098577304361554745], [17990358020177143287, "quote", false, 9184285660066293423]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-3509b2226e8de3b4\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}