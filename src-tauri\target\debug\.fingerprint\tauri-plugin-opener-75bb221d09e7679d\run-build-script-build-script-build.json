{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17045726903344650895, "build_script_build", false, 12405375093056775087], [16429266147849286097, "build_script_build", false, 3142651792696718635]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-opener-75bb221d09e7679d\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}