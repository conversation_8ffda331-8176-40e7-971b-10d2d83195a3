# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-get"
description = "Enables the get command without any pre-configured scope."
commands.allow = ["get"]

[[permission]]
identifier = "deny-get"
description = "Denies the get command without any pre-configured scope."
commands.deny = ["get"]
