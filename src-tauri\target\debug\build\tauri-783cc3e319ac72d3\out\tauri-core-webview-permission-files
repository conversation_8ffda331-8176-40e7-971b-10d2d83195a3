["\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\clear_all_browsing_data.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\create_webview.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\create_webview_window.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\get_all_webviews.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\internal_toggle_devtools.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\print.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\reparent.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_auto_resize.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_background_color.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_focus.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_position.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_size.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\set_webview_zoom.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\webview_close.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\webview_hide.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\webview_position.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\webview_show.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\commands\\webview_size.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\webview\\autogenerated\\default.toml"]