{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2241668132362809309, "path": 14055119943448302808, "deps": [[503635761244294217, "regex", false, 4881237336441774592], [2001188854233969420, "build_script_build", false, 8266128884065632469], [4352886507220678900, "serde_json", false, 8129347805295222210], [4537297827336760846, "thiserror", false, 9390898151654131111], [5986029879202738730, "log", false, 7774817665610071283], [9689903380558560274, "serde", false, 3343704796167280671], [11337703028400419576, "os_pipe", false, 11522099092155239669], [14564311161534545801, "encoding_rs", false, 12886539549651415333], [15722096100444777195, "shared_child", false, 2283285924675971291], [16192041687293812804, "open", false, 8844439091371646615], [17045726903344650895, "tauri", false, 7269751384454844449], [17531218394775549125, "tokio", false, 383937516766407131]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-b8d46d5e7962ef99\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}