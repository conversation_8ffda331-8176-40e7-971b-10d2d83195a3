{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2241668132362809309, "path": 8979343188923196492, "deps": [[376837177317575824, "build_script_build", false, 7514445796687627841], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [5986029879202738730, "log", false, 7774817665610071283], [10281541584571964250, "windows_sys", false, 5840527374877426736]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-509b2330092617c5\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}