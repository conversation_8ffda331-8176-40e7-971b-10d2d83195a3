{"rustc": 3062648155896360161, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 11377959802098117882, "deps": [[2995469292676432503, "uuid1", false, 2000887656250757052], [4352886507220678900, "serde_json", false, 8416949242721867315], [5404511084185685755, "url", false, 1119675027400249091], [6913375703034175521, "build_script_build", false, 15345761691232523873], [6982418085031928086, "dyn_clone", false, 1301159903792534948], [9689903380558560274, "serde", false, 10158412727755539621], [14923790796823607459, "indexmap", false, 16513217922764047296], [16071897500792579091, "schemars_derive", false, 1593246454549666037]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-ede9ce625068fd8b\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}