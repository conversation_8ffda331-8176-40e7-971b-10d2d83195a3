{"rustc": 3062648155896360161, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 17665468728698760178, "deps": [[4352886507220678900, "serde_json", false, 8416949242721867315], [4824857623768494398, "cargo_toml", false, 15787524207170468755], [4899080583175475170, "semver", false, 8779510558805760367], [5165059047667588304, "tauri_winres", false, 10856166667384693281], [6913375703034175521, "schemars", false, 15773716390437194172], [7170110829644101142, "json_patch", false, 9131228490950987641], [9293239362693504808, "glob", false, 15249143244556817786], [9689903380558560274, "serde", false, 10158412727755539621], [11207653606310558077, "anyhow", false, 10570222046967116191], [12060164242600251039, "toml", false, 471057767459257420], [13077543566650298139, "heck", false, 3582930420090025893], [15622660310229662834, "walkdir", false, 4258291066958692746], [16928111194414003569, "dirs", false, 12773387142383182090], [17233053221795943287, "tauri_utils", false, 6114817519717018938]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-8b9350c540ae0094\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}