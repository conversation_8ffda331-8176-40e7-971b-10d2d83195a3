{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17045726903344650895, "build_script_build", false, 17577413450113700303], [16429266147849286097, "build_script_build", false, 4238745878185794383]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-opener-9ea45674c81472a5\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}