{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 14055119943448302808, "deps": [[503635761244294217, "regex", false, 1200075957435756650], [2001188854233969420, "build_script_build", false, 15515555651374510246], [4352886507220678900, "serde_json", false, 3514748254459580741], [4537297827336760846, "thiserror", false, 14182969183628281155], [5986029879202738730, "log", false, 7362680585957259537], [9689903380558560274, "serde", false, 15997447206965605229], [11337703028400419576, "os_pipe", false, 2488524117652108387], [14564311161534545801, "encoding_rs", false, 12295206044409703602], [15722096100444777195, "shared_child", false, 8048407801816929231], [16192041687293812804, "open", false, 16823990433367447657], [17045726903344650895, "tauri", false, 3932899258046728249], [17531218394775549125, "tokio", false, 12989514077590107793]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-07f5ac7ea24e5cc9\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}