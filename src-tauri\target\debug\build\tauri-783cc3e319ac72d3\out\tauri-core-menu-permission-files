["\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-783cc3e319ac72d3\\out\\permissions\\menu\\autogenerated\\default.toml"]