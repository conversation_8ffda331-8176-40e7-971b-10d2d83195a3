cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\github\Screen Recording\src-tauri\target\debug\build\tauri-plugin-dialog-9ac4b581c5caa7c5\out\tauri-plugin-dialog-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-dialog-2.3.3\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
