{"$message_type":"diagnostic","message":"borrow of moved value: `path`","code":{"code":"E0382","explanation":"A variable was used after its contents have been moved elsewhere.\n\nErroneous code example:\n\n```compile_fail,E0382\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = MyStruct{ s: 5u32 };\n    let y = x;\n    x.s = 6;\n    println!(\"{}\", x.s);\n}\n```\n\nSince `MyStruct` is a type that is not marked `Copy`, the data gets moved out\nof `x` when we set `y`. This is fundamental to Rust's ownership system: outside\nof workarounds like `Rc`, a value cannot be owned by more than one variable.\n\nSometimes we don't need to move the value. Using a reference, we can let another\nfunction borrow the value without changing its ownership. In the example below,\nwe don't actually have to move our string to `calculate_length`, we can give it\na reference to it with `&` instead.\n\n```\nfn main() {\n    let s1 = String::from(\"hello\");\n\n    let len = calculate_length(&s1);\n\n    println!(\"The length of '{}' is {}.\", s1, len);\n}\n\nfn calculate_length(s: &String) -> usize {\n    s.len()\n}\n```\n\nA mutable reference can be created with `&mut`.\n\nSometimes we don't want a reference, but a duplicate. All types marked `Clone`\ncan be duplicated by calling `.clone()`. Subsequent changes to a clone do not\naffect the original variable.\n\nMost types in the standard library are marked `Clone`. The example below\ndemonstrates using `clone()` on a string. `s1` is first set to \"many\", and then\ncopied to `s2`. Then the first character of `s1` is removed, without affecting\n`s2`. \"any many\" is printed to the console.\n\n```\nfn main() {\n    let mut s1 = String::from(\"many\");\n    let s2 = s1.clone();\n    s1.remove(0);\n    println!(\"{} {}\", s1, s2);\n}\n```\n\nIf we control the definition of a type, we can implement `Clone` on it ourselves\nwith `#[derive(Clone)]`.\n\nSome types have no ownership semantics at all and are trivial to duplicate. An\nexample is `i32` and the other number types. We don't have to call `.clone()` to\nclone them, because they are marked `Copy` in addition to `Clone`. Implicit\ncloning is more convenient in this case. We can mark our own types `Copy` if\nall their members also are marked `Copy`.\n\nIn the example below, we implement a `Point` type. Because it only stores two\nintegers, we opt-out of ownership semantics with `Copy`. Then we can\n`let p2 = p1` without `p1` being moved.\n\n```\n#[derive(Copy, Clone)]\nstruct Point { x: i32, y: i32 }\n\nfn main() {\n    let mut p1 = Point{ x: -1, y: 2 };\n    let p2 = p1;\n    p1.x = 1;\n    println!(\"p1: {}, {}\", p1.x, p1.y);\n    println!(\"p2: {}, {}\", p2.x, p2.y);\n}\n```\n\nAlternatively, if we don't control the struct's definition, or mutable shared\nownership is truly required, we can use `Rc` and `RefCell`:\n\n```\nuse std::cell::RefCell;\nuse std::rc::Rc;\n\nstruct MyStruct { s: u32 }\n\nfn main() {\n    let mut x = Rc::new(RefCell::new(MyStruct{ s: 5u32 }));\n    let y = x.clone();\n    x.borrow_mut().s = 6;\n    println!(\"{}\", x.borrow().s);\n}\n```\n\nWith this approach, x and y share ownership of the data via the `Rc` (reference\ncount type). `RefCell` essentially performs runtime borrow checking: ensuring\nthat at most one writer or multiple readers can access the data at any one time.\n\nIf you wish to learn more about ownership in Rust, start with the\n[Understanding Ownership][understanding-ownership] chapter in the Book.\n\n[understanding-ownership]: https://doc.rust-lang.org/book/ch04-00-understanding-ownership.html\n"},"level":"error","spans":[{"file_name":"src\\lib.rs","byte_start":3135,"byte_end":3142,"line_start":111,"line_end":111,"column_start":46,"column_end":53,"is_primary":false,"text":[{"text":"    let result = tokio::task::spawn_blocking(move || {","highlight_start":46,"highlight_end":53}],"label":"value moved into closure here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3256,"byte_end":3260,"line_start":115,"line_end":115,"column_start":23,"column_end":27,"is_primary":false,"text":[{"text":"                .arg(&path)","highlight_start":23,"highlight_end":27}],"label":"variable moved due to use in closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\lib.rs","byte_start":3813,"byte_end":3817,"line_start":138,"line_end":138,"column_start":42,"column_end":46,"is_primary":true,"text":[{"text":"                    info!(\"成功打开文件夹: {}\", path);","highlight_start":42,"highlight_end":46}],"label":"value borrowed here after move","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":101370,"byte_end":101418,"line_start":2961,"line_end":2961,"column_start":79,"column_end":127,"is_primary":false,"text":[{"text":"        $crate::valueset!(@ { (&$next, $crate::__macro_support::Option::Some(&$crate::__macro_support::format_args!($($rest)+) as &dyn Value)), $($out),* }, $next, )","highlight_start":79,"highlight_end":127}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":101370,"byte_end":101418,"line_start":2961,"line_end":2961,"column_start":79,"column_end":127,"is_primary":false,"text":[{"text":"        $crate::valueset!(@ { (&$next, $crate::__macro_support::Option::Some(&$crate::__macro_support::format_args!($($rest)+) as &dyn Value)), $($out),* }, $next, )","highlight_start":79,"highlight_end":127}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":101700,"byte_end":101893,"line_start":2970,"line_end":2974,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            $fields.value_set($crate::valueset!(","highlight_start":31,"highlight_end":49},{"text":"                @ { },","highlight_start":1,"highlight_end":23},{"text":"                $crate::__macro_support::Iterator::next(&mut iter).expect(\"FieldSet corrupted (this is a bug)\"),","highlight_start":1,"highlight_end":113},{"text":"                $($kvs)+","highlight_start":1,"highlight_end":25},{"text":"            ))","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":28037,"byte_end":28099,"line_start":884,"line_end":884,"column_start":16,"column_end":78,"is_primary":false,"text":[{"text":"            })($crate::valueset!(__CALLSITE.metadata().fields(), $($fields)*));","highlight_start":16,"highlight_end":78}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":28791,"byte_end":28842,"line_start":904,"line_end":904,"column_start":9,"column_end":60,"is_primary":false,"text":[{"text":"        $crate::event!(target: $target, $lvl, { $($arg)+ })","highlight_start":9,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":71407,"byte_end":71522,"line_start":2110,"line_end":2114,"column_start":9,"column_end":10,"is_primary":false,"text":[{"text":"        $crate::event!(","highlight_start":9,"highlight_end":24},{"text":"            target: module_path!(),","highlight_start":1,"highlight_end":36},{"text":"            $crate::Level::INFO,","highlight_start":1,"highlight_end":33},{"text":"            $($arg)+","highlight_start":1,"highlight_end":21},{"text":"        )","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\lib.rs","byte_start":3778,"byte_end":3818,"line_start":138,"line_end":138,"column_start":21,"column_end":47,"is_primary":false,"text":[{"text":"                    info!(\"成功打开文件夹: {}\", path);","highlight_start":21,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"info!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":62152,"byte_end":62169,"line_start":1866,"line_end":1866,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! info {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::event!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":16449,"byte_end":16467,"line_start":585,"line_end":585,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! event {","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::valueset!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\tracing-0.1.41\\src\\macros.rs","byte_start":94593,"byte_end":94614,"line_start":2784,"line_end":2784,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! valueset {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::__macro_support::format_args!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\macros\\mod.rs","byte_start":34899,"byte_end":34923,"line_start":1004,"line_end":1004,"column_start":5,"column_end":29,"is_primary":false,"text":[{"text":"    macro_rules! format_args {","highlight_start":5,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"desugaring of format string literal","def_site_span":{"file_name":"src\\lib.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":false,"text":[],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"src\\lib.rs","byte_start":2960,"byte_end":2964,"line_start":106,"line_end":106,"column_start":22,"column_end":26,"is_primary":false,"text":[{"text":"async fn open_folder(path: String) -> Result<(), String> {","highlight_start":22,"highlight_end":26}],"label":"move occurs because `path` has type `std::string::String`, which does not implement the `Copy` trait","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0382]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: borrow of moved value: `path`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\lib.rs:138:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn open_folder(path: String) -> Result<(), String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmove occurs because `path` has type `std::string::String`, which does not implement the `Copy` trait\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let result = tokio::task::spawn_blocking(move || {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvalue moved into closure here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .arg(&path)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariable moved due to use in closure\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    info!(\"成功打开文件夹: {}\", path);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mvalue borrowed here after move\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::__macro_support::format_args` which comes from the expansion of the macro `info` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0382`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0382`.\u001b[0m\n"}
