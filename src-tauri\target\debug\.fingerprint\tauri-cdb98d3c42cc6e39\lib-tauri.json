{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 7461092880057568245, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6596700188327232920], [1260461579271933187, "serialize_to_javascript", false, 18403164339601158030], [1967864351173319501, "muda", false, 8031816090766571352], [2013030631243296465, "webview2_com", false, 13060970121218249020], [3331586631144870129, "getrandom", false, 6780815000201805298], [4143744114649553716, "raw_window_handle", false, 6746238607539022165], [4352886507220678900, "serde_json", false, 3514748254459580741], [4537297827336760846, "thiserror", false, 14182969183628281155], [5404511084185685755, "url", false, 8679425891305281097], [5986029879202738730, "log", false, 7362680585957259537], [6537120525306722933, "tauri_macros", false, 2581433363310509070], [6803352382179706244, "percent_encoding", false, 11626513121077668949], [9010263965687315507, "http", false, 5658707743831154247], [9293239362693504808, "glob", false, 18154582957794916575], [9689903380558560274, "serde", false, 15997447206965605229], [9952368442187680820, "tauri_runtime_wry", false, 2863504129117206702], [10229185211513642314, "mime", false, 16446765585379284014], [11207653606310558077, "anyhow", false, 2253731331918607064], [11989259058781683633, "dunce", false, 9201657431270293002], [12565293087094287914, "window_vibrancy", false, 7933958384367355208], [12986574360607194341, "serde_repr", false, 11926449676126731677], [13077543566650298139, "heck", false, 7893566072762738503], [14585479307175734061, "windows", false, 7951036826101499968], [16727543399706004146, "cookie", false, 12847614340541995132], [16928111194414003569, "dirs", false, 1360072726882868404], [17045726903344650895, "build_script_build", false, 17577413450113700303], [17233053221795943287, "tauri_utils", false, 7279738404176155259], [17531218394775549125, "tokio", false, 12989514077590107793], [18010483002580779355, "tauri_runtime", false, 12517948802053664307], [18035788301859549979, "tray_icon", false, 16712285157871905633]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-cdb98d3c42cc6e39\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}