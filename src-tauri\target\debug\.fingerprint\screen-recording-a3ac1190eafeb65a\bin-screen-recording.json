{"rustc": 3062648155896360161, "features": "[]", "declared_features": "[]", "target": 17100642083304633379, "profile": 17672942494452627365, "path": 4942398508502643691, "deps": [[1630737303963722877, "tauri_plugin_dialog", false, 7649096414724587203], [2001188854233969420, "tauri_plugin_shell", false, 5889048049220715102], [2120440849614942281, "tauri_plugin_store", false, 6549132134188534458], [4352886507220678900, "serde_json", false, 8129347805295222210], [8606274917505247608, "tracing", false, 1705463880016604026], [9689903380558560274, "serde", false, 3343704796167280671], [11457472658001459637, "tauri_plugin_global_shortcut", false, 7502373629051307881], [14285978758320820277, "tauri_plugin_fs", false, 16562648205378801321], [16230660778393187092, "tracing_subscriber", false, 4896030790957205725], [16429266147849286097, "tauri_plugin_opener", false, 12390509091193429870], [16687502426821174256, "screen_recording_lib", false, 3808635129734777907], [16687502426821174256, "build_script_build", false, 9330698443746188260], [17045726903344650895, "tauri", false, 7269751384454844449], [17531218394775549125, "tokio", false, 383937516766407131]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\screen-recording-a3ac1190eafeb65a\\dep-bin-screen-recording", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}