cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=D:\github\Screen Recording\src-tauri\target\debug\build\tauri-plugin-store-249f25c658d95c42\out\tauri-plugin-store-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-store-2.4.0\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
