{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 2684675381532056621, "deps": [[376837177317575824, "softbuffer", false, 4328728033272473007], [2013030631243296465, "webview2_com", false, 5927907557082522975], [2172092324659420098, "tao", false, 16618500773145613772], [3722963349756955755, "once_cell", false, 12245490560791113519], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [5404511084185685755, "url", false, 10335955844979737016], [5986029879202738730, "log", false, 7774817665610071283], [9010263965687315507, "http", false, 13386383315247088926], [9952368442187680820, "build_script_build", false, 12913848232493947529], [10279713220953215556, "wry", false, 17295867143796560173], [14585479307175734061, "windows", false, 1114852230663379918], [17233053221795943287, "tauri_utils", false, 15618318111205922455], [18010483002580779355, "tauri_runtime", false, 41703060522131209]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-13f5eb985e84d7ec\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}