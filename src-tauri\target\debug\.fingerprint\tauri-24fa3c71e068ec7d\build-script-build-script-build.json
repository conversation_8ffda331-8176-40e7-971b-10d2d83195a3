{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"macos-private-api\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 10896516689019957532, "deps": [[9293239362693504808, "glob", false, 18154582957794916575], [13077543566650298139, "heck", false, 7893566072762738503], [16195641215173142501, "tauri_build", false, 7408128993171316260], [17233053221795943287, "tauri_utils", false, 15823093137894587422]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-24fa3c71e068ec7d\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}