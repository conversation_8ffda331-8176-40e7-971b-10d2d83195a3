{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16687502426821174256, "build_script_build", false, 1645099573792641922], [17045726903344650895, "build_script_build", false, 13189999022037239311], [1630737303963722877, "build_script_build", false, 10135934456968668635], [14285978758320820277, "build_script_build", false, 7504531367538067811], [11457472658001459637, "build_script_build", false, 5006577822817263080], [16429266147849286097, "build_script_build", false, 15783646166108407284], [2001188854233969420, "build_script_build", false, 15515555651374510246], [2120440849614942281, "build_script_build", false, 11630154992060313341]], "local": [{"RerunIfChanged": {"output": "debug\\build\\screen-recording-9b4e4dcc94df5d84\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}