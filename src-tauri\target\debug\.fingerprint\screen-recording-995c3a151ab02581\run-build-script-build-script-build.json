{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16687502426821174256, "build_script_build", false, 1423228332867379584], [17045726903344650895, "build_script_build", false, 5684679338262672637], [1630737303963722877, "build_script_build", false, 352638777621919245], [14285978758320820277, "build_script_build", false, 5449094223027364148], [11457472658001459637, "build_script_build", false, 4239989851024374320], [16429266147849286097, "build_script_build", false, 13426436987199979890], [2001188854233969420, "build_script_build", false, 8266128884065632469], [2120440849614942281, "build_script_build", false, 4844352901634926744]], "local": [{"RerunIfChanged": {"output": "debug\\build\\screen-recording-995c3a151ab02581\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}