{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 14868551241969206162, "deps": [[373107762698212489, "proc_macro2", false, 17845992585597592650], [1678291836268844980, "brotli", false, 16798946941339883375], [2995469292676432503, "uuid", false, 2000887656250757052], [4352886507220678900, "serde_json", false, 8416949242721867315], [4537297827336760846, "thiserror", false, 1350304123696274477], [4899080583175475170, "semver", false, 8779510558805760367], [5404511084185685755, "url", false, 1119675027400249091], [7170110829644101142, "json_patch", false, 9131228490950987641], [7392050791754369441, "ico", false, 16301792672088788709], [9689903380558560274, "serde", false, 10158412727755539621], [9857275760291862238, "sha2", false, 8404823434255167925], [12687914511023397207, "png", false, 12751997551168288492], [13077212702700853852, "base64", false, 6996375772622157035], [15622660310229662834, "walkdir", false, 4258291066958692746], [17233053221795943287, "tauri_utils", false, 6114817519717018938], [17332570067994900305, "syn", false, 14098577304361554745], [17990358020177143287, "quote", false, 9184285660066293423]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-f2e22dac6a34d107\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}