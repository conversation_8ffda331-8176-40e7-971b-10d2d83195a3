{"rustc": 3062648155896360161, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 13021828739765011656, "deps": [[373107762698212489, "proc_macro2", false, 17845992585597592650], [503635761244294217, "regex", false, 6312751479177319585], [1200537532907108615, "url<PERSON><PERSON>n", false, 11159323806331544475], [1678291836268844980, "brotli", false, 16798946941339883375], [2995469292676432503, "uuid", false, 2000887656250757052], [4071963112282141418, "serde_with", false, 1304925108159507132], [4352886507220678900, "serde_json", false, 8416949242721867315], [4537297827336760846, "thiserror", false, 1350304123696274477], [4899080583175475170, "semver", false, 8779510558805760367], [5404511084185685755, "url", false, 1119675027400249091], [5986029879202738730, "log", false, 12256801952257639155], [6606131838865521726, "ctor", false, 5478961089336400257], [6913375703034175521, "schemars", false, 15773716390437194172], [7170110829644101142, "json_patch", false, 9131228490950987641], [9010263965687315507, "http", false, 9998988821602321615], [9293239362693504808, "glob", false, 15249143244556817786], [9689903380558560274, "serde", false, 10158412727755539621], [11207653606310558077, "anyhow", false, 10570222046967116191], [11655476559277113544, "cargo_metadata", false, 10491779496806645101], [11989259058781683633, "dunce", false, 8627539664445644455], [12060164242600251039, "toml", false, 471057767459257420], [14232843520438415263, "html5ever", false, 9408291493561602891], [15088007382495681292, "kuchiki", false, 4738208906819494970], [15622660310229662834, "walkdir", false, 4258291066958692746], [15932120279885307830, "memchr", false, 7864981923312058874], [17146114186171651583, "infer", false, 4164556388310731990], [17183029615630212089, "serde_untagged", false, 6368577479460357967], [17186037756130803222, "phf", false, 9178334777372973577], [17990358020177143287, "quote", false, 9184285660066293423]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-3549ae2f5e4a9029\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}