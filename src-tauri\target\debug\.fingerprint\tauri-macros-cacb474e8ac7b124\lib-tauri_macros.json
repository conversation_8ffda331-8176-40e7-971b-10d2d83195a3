{"rustc": 3062648155896360161, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 16594089302644043547, "deps": [[373107762698212489, "proc_macro2", false, 17845992585597592650], [3412097196613774653, "tauri_codegen", false, 10372579122881233250], [13077543566650298139, "heck", false, 7893566072762738503], [17233053221795943287, "tauri_utils", false, 15823093137894587422], [17332570067994900305, "syn", false, 14098577304361554745], [17990358020177143287, "quote", false, 9184285660066293423]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-cacb474e8ac7b124\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}