["\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\tauri-a4efadfff4fca75a\\out\\permissions\\path\\autogenerated\\default.toml"]