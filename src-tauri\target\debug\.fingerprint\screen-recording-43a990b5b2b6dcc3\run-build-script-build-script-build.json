{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16687502426821174256, "build_script_build", false, 1423228332867379584], [17045726903344650895, "build_script_build", false, 12405375093056775087], [1630737303963722877, "build_script_build", false, 12198697265124843791], [14285978758320820277, "build_script_build", false, 4542868154138831006], [11457472658001459637, "build_script_build", false, 18333192731142522352], [16429266147849286097, "build_script_build", false, 8476785661180547277], [2001188854233969420, "build_script_build", false, 11071487106109544060], [2120440849614942281, "build_script_build", false, 11119120273292343101]], "local": [{"RerunIfChanged": {"output": "debug\\build\\screen-recording-43a990b5b2b6dcc3\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}