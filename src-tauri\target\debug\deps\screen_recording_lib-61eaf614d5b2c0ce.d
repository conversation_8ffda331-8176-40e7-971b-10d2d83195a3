D:\github\Screen Recording\src-tauri\target\debug\deps\screen_recording_lib-61eaf614d5b2c0ce.d: src\lib.rs D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7 D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/c48520395bd7656d707aeb2bd96bf235b9e9fda0998cb3ffca2381a5663f13ae

D:\github\Screen Recording\src-tauri\target\debug\deps\libscreen_recording_lib-61eaf614d5b2c0ce.rmeta: src\lib.rs D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7 D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/c48520395bd7656d707aeb2bd96bf235b9e9fda0998cb3ffca2381a5663f13ae

src\lib.rs:
D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:
D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-995c3a151ab02581\out/c48520395bd7656d707aeb2bd96bf235b9e9fda0998cb3ffca2381a5663f13ae:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=screen-recording
# env-dep:OUT_DIR=D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\screen-recording-995c3a151ab02581\\out
