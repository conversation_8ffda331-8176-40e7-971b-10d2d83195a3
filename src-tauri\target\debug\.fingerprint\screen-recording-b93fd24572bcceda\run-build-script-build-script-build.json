{"rustc": 3062648155896360161, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[16687502426821174256, "build_script_build", false, 1645099573792641922], [17045726903344650895, "build_script_build", false, 17577413450113700303], [1630737303963722877, "build_script_build", false, 10377762285535747382], [14285978758320820277, "build_script_build", false, 2724777540877805381], [11457472658001459637, "build_script_build", false, 16719944002229073737], [16429266147849286097, "build_script_build", false, 9045444732600151169], [2001188854233969420, "build_script_build", false, 16657537203663998242], [2120440849614942281, "build_script_build", false, 16373268881635735808]], "local": [{"RerunIfChanged": {"output": "debug\\build\\screen-recording-b93fd24572bcceda\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}