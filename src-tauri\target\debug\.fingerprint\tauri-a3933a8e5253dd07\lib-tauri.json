{"rustc": 3062648155896360161, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 7461092880057568245, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 13970908537300600685], [1260461579271933187, "serialize_to_javascript", false, 10273044751840655269], [1967864351173319501, "muda", false, 12159562583417331378], [2013030631243296465, "webview2_com", false, 5927907557082522975], [3331586631144870129, "getrandom", false, 169603251186936355], [4143744114649553716, "raw_window_handle", false, 9063517901930602415], [4352886507220678900, "serde_json", false, 8129347805295222210], [4537297827336760846, "thiserror", false, 9390898151654131111], [5404511084185685755, "url", false, 10335955844979737016], [5986029879202738730, "log", false, 7774817665610071283], [6537120525306722933, "tauri_macros", false, 4298033341989461961], [6803352382179706244, "percent_encoding", false, 12344142938761695273], [************7315507, "http", false, 13386383315247088926], [9293239362693504808, "glob", false, 4660908313084390176], [9689903380558560274, "serde", false, 3343704796167280671], [9952368442187680820, "tauri_runtime_wry", false, 6389306335864581935], [10229185211513642314, "mime", false, 1010667508167487769], [11207653606310558077, "anyhow", false, 536122387551949549], [11989259058781683633, "dunce", false, 6908640956392211055], [12565293087094287914, "window_vibrancy", false, 14871856546183362541], [12986574360607194341, "serde_repr", false, 11926449676126731677], [13077543566650298139, "heck", false, 14691219344582192275], [14585479307175734061, "windows", false, 1114852230663379918], [16727543399706004146, "cookie", false, 13570237757606925530], [16928111194414003569, "dirs", false, 15588576381945972211], [17045726903344650895, "build_script_build", false, 5684679338262672637], [17233053221795943287, "tauri_utils", false, 15215227482654165719], [17531218394775549125, "tokio", false, 383937516766407131], [18010483002580779355, "tauri_runtime", false, 2987339046282938220], [18035788301859549979, "tray_icon", false, 13909417756861163304]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-a3933a8e5253dd07\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}