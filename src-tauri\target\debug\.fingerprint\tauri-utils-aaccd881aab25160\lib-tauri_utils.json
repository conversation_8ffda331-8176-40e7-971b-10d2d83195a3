{"rustc": 3062648155896360161, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 13021828739765011656, "deps": [[503635761244294217, "regex", false, 1200075957435756650], [1200537532907108615, "url<PERSON><PERSON>n", false, 6596700188327232920], [1678291836268844980, "brotli", false, 8238398934390701518], [2995469292676432503, "uuid", false, 3565742873933080452], [4071963112282141418, "serde_with", false, 1142110593720651167], [4352886507220678900, "serde_json", false, 3514748254459580741], [4537297827336760846, "thiserror", false, 14182969183628281155], [4899080583175475170, "semver", false, 10602273900205224635], [5404511084185685755, "url", false, 8679425891305281097], [5986029879202738730, "log", false, 7362680585957259537], [6606131838865521726, "ctor", false, 5478961089336400257], [7170110829644101142, "json_patch", false, 6040600717806411937], [9010263965687315507, "http", false, 5658707743831154247], [9293239362693504808, "glob", false, 18154582957794916575], [9689903380558560274, "serde", false, 15997447206965605229], [11207653606310558077, "anyhow", false, 2253731331918607064], [11989259058781683633, "dunce", false, 9201657431270293002], [12060164242600251039, "toml", false, 7603385891349780476], [15622660310229662834, "walkdir", false, 10789997580310503149], [15932120279885307830, "memchr", false, 16813453770321735391], [17146114186171651583, "infer", false, 16797252734279427881], [17183029615630212089, "serde_untagged", false, 9037437929187716537], [17186037756130803222, "phf", false, 12467581866238513660]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-aaccd881aab25160\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}