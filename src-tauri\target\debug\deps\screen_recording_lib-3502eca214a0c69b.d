D:\github\Screen Recording\src-tauri\target\debug\deps\screen_recording_lib-3502eca214a0c69b.d: src\lib.rs src\../icons/icon.png D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-43a990b5b2b6dcc3\out/bd964268f6104387acd25aed0f85c8abb95300d16e1d972e57e65e5b97e5a89b

D:\github\Screen Recording\src-tauri\target\debug\deps\libscreen_recording_lib-3502eca214a0c69b.rmeta: src\lib.rs src\../icons/icon.png D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-43a990b5b2b6dcc3\out/bd964268f6104387acd25aed0f85c8abb95300d16e1d972e57e65e5b97e5a89b

src\lib.rs:
src\../icons/icon.png:
D:\github\Screen\ Recording\src-tauri\target\debug\build\screen-recording-43a990b5b2b6dcc3\out/bd964268f6104387acd25aed0f85c8abb95300d16e1d972e57e65e5b97e5a89b:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=screen-recording
# env-dep:OUT_DIR=D:\\github\\Screen Recording\\src-tauri\\target\\debug\\build\\screen-recording-43a990b5b2b6dcc3\\out
